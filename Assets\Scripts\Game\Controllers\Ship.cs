// ───────────────────────────────────────
// Ship.cs  • cleaned version
// ───────────────────────────────────────
using UnityEngine;

[RequireComponent(typeof(Rigidbody))]
public class Ship : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
{
    /* ── Inspector ───────────────────────────────────────────── */
    [Head<PERSON>("References")]
    [SerializeField] InputSettings inputSettings;
    [SerializeField] Transform hatch;
    [SerializeField] float hatchAngle = 85f;
    [SerializeField] Transform camViewPoint;
    [SerializeField] Transform pilotSeatPoint;
    [Serial<PERSON><PERSON>ield] LayerMask groundMask;
    [SerializeField] GameObject window;
    [SerializeField] ShipGravity shipGravity;
    [Serial<PERSON>F<PERSON>] Interactable flightControls;

    [<PERSON><PERSON>("Physics Materials")]
    [SerializeField] PhysicsMaterial exteriorMaterial;  // High friction for landing
    [SerializeField] PhysicsMaterial interiorMaterial;  // Low friction for player movement

    [Header("Flight handling")]
    [SerializeField] float thrustStrength = 20f;
    [SerializeField] float rotSpeed = 5f;
    [SerializeField] float rollSpeed = 30f;
    [SerializeField] float rotSmoothSpeed = 10f;

    /* ── Private state ───────────────────────────────────────── */
    Rigidbody rb;
    Quaternion targetRot;
    Quaternion smoothedRot;
    Vector3 thrusterInput;
    PlayerController pilot;                     // player who is currently piloting
    bool shipIsPiloted;
    int groundContacts;
    bool hatchOpen;

    /* ── Key bindings (feel free to expose if desired) ───────── */
    readonly KeyCode ascendKey = KeyCode.Space;
    readonly KeyCode descendKey = KeyCode.LeftShift;
    readonly KeyCode rollLeftKey = KeyCode.Q;
    readonly KeyCode rollRightKey = KeyCode.E;
    readonly KeyCode forwardKey = KeyCode.W;
    readonly KeyCode backwardKey = KeyCode.S;
    readonly KeyCode leftKey = KeyCode.A;
    readonly KeyCode rightKey = KeyCode.D;

    /* ─────────────────────────────────────────────────────────── */
    void Awake()
    {
        rb = GetComponent<Rigidbody>();
        rb.useGravity = false;
        rb.interpolation = RigidbodyInterpolation.Interpolate;

        // ensure ShipGravity exists
        shipGravity ??= GetComponent<ShipGravity>() ??
                        gameObject.AddComponent<ShipGravity>();

        targetRot = transform.rotation;
        smoothedRot = transform.rotation;

        inputSettings?.Begin();

        // Setup physics materials for proper interior/exterior friction
        SetupPhysicsMaterials();
    }

    /* ─────────────────────────────────────────────────────────── */
    void Update()
    {
        if (shipIsPiloted) HandleFlightInput();
        AnimateHatch();
        HandleCheatTeleport();
    }

    void FixedUpdate()
    {
        // thrust
        rb.AddForce(transform.TransformVector(thrusterInput) *
                    thrustStrength, ForceMode.Acceleration);

        // rotation
        if (groundContacts == 0)
            rb.MoveRotation(smoothedRot);
    }

    /* ── Public interface ────────────────────────────────────── */
    public Vector3 GetArtificialGravityDirection() => -transform.up;
    public bool IsPiloted => shipIsPiloted;
    public bool HatchOpen => hatchOpen;

    public void ToggleHatch() => hatchOpen = !hatchOpen;
    public void TogglePiloting()
    {
        if (shipIsPiloted) StopPiloting();
        else StartPiloting(FindObjectOfType<PlayerController>());
    }

    public void SetVelocity(Vector3 velocity)
    {
        rb.linearVelocity = velocity;
    }

    /* ── Walking-inside hook (called from ShipGravity)────────── */
    public void EnterShipInterior(PlayerController pc)
    {
        // At the moment we only need the reference for gravity;
        // nothing else is required here because we do not parent or
        // disable the player when they are walking around.
    }

    /* ── Private helpers ─────────────────────────────────────── */
    void HandleFlightInput()
    {
        // translational thrust
        int x = Axis(leftKey, rightKey);
        int y = Axis(descendKey, ascendKey);
        int z = Axis(backwardKey, forwardKey);
        thrusterInput = new Vector3(x, y, z);

        // rotational input
        float yaw = Input.GetAxisRaw("Mouse X") *
                      rotSpeed * inputSettings.mouseSensitivity / 100f;
        float pitch = Input.GetAxisRaw("Mouse Y") *
                      rotSpeed * inputSettings.mouseSensitivity / 100f;
        float roll = Axis(rollLeftKey, rollRightKey) *
                      rollSpeed * Time.deltaTime;

        if (groundContacts == 0)
        {
            Quaternion qYaw = Quaternion.AngleAxis(yaw, transform.up);
            Quaternion qPitch = Quaternion.AngleAxis(-pitch, transform.right);
            Quaternion qRoll = Quaternion.AngleAxis(-roll, transform.forward);

            targetRot = qYaw * qPitch * qRoll * targetRot;
            smoothedRot = Quaternion.Slerp(transform.rotation, targetRot,
                                           Time.deltaTime * rotSmoothSpeed);
        }
        else  // grounded: freeze rotation
        {
            targetRot = smoothedRot = transform.rotation;
        }
    }

    void StartPiloting(PlayerController pc)
    {
        if (pc == null) return;

        pilot = pc;
        shipIsPiloted = true;
        hatchOpen = false;
        window.SetActive(false);

        // hand camera to cockpit
        var cam = pilot.Camera.transform;
        cam.SetParent(camViewPoint);
        cam.localPosition = Vector3.zero;
        cam.localRotation = Quaternion.identity;

        // hide player avatar while seated
        pilot.gameObject.SetActive(false);
    }

    void StopPiloting()
    {
        shipIsPiloted = false;
        window.SetActive(true);

        // Calculate proper orientation relative to ship's current gravity
        Vector3 shipGravityDir = GetArtificialGravityDirection();
        Vector3 shipUp = -shipGravityDir;

        // Position player at seat but orient them properly to ship gravity
        pilot.transform.position = pilotSeatPoint.position;

        // Orient player to stand upright relative to ship's gravity
        Vector3 playerForward = Vector3.ProjectOnPlane(pilotSeatPoint.forward, shipUp);
        if (playerForward.sqrMagnitude < 0.001f)
            playerForward = Vector3.ProjectOnPlane(transform.forward, shipUp);

        pilot.transform.rotation = Quaternion.LookRotation(playerForward, shipUp);

        // Inherit ship velocity and add slight upward velocity to prevent clipping
        pilot.Rigidbody.linearVelocity = rb.linearVelocity + shipUp * 0.5f;

        // give camera back
        var cam = pilot.Camera.transform;
        cam.SetParent(pilot.transform);
        cam.localPosition = pilot.CameraInitialLocalPos;
        cam.localRotation = Quaternion.identity;

        pilot.gameObject.SetActive(true);

        // Keep player in ship interior mode to maintain ship gravity
        pilot.EnterShipInterior(this);

        pilot = null;
    }

    int Axis(KeyCode negative, KeyCode positive)
    {
        int v = 0;
        if (Input.GetKey(positive)) v++;
        if (Input.GetKey(negative)) v--;
        return v;
    }

    /* ── Hatch animation ─────────────────────────────────────── */
    void AnimateHatch()
    {
        float tgt = hatchOpen ? hatchAngle : 0f;
        Vector3 e = hatch.localEulerAngles;
        e.x = Mathf.LerpAngle(e.x, tgt, Time.deltaTime * 8f);
        hatch.localEulerAngles = e;
    }

    /* ── Simple cheat teleport (unchanged) ───────────────────── */
    void HandleCheatTeleport()
    {
        if (!Universe.cheatsEnabled ||
            !shipIsPiloted ||
            Time.timeScale == 0)
            return;

        if (Input.GetKeyDown(KeyCode.Return))
        {
            var hud = FindObjectOfType<ShipHUD>();
            if (hud && hud.LockedBody) TeleportToBody(hud.LockedBody);
        }
    }

    void TeleportToBody(CelestialBody body)
    {
        rb.linearVelocity = body.GetVelocity();
        Vector3 dir = (transform.position - body.transform.position).normalized;
        rb.MovePosition(body.transform.position + dir * body.radius * 2f);
    }

    /* ── Ground contact counting ─────────────────────────────── */
    void OnCollisionEnter(Collision col)
    {
        if (((1 << col.gameObject.layer) & groundMask) != 0)
            groundContacts++;
    }

    void OnCollisionExit(Collision col)
    {
        if (((1 << col.gameObject.layer) & groundMask) != 0)
            groundContacts--;
    }

    /* ── Physics Materials Management ────────────────────────── */
    void SetupPhysicsMaterials()
    {
        // Load default materials if not assigned
        if (exteriorMaterial == null)
        {
            exteriorMaterial = Resources.Load<PhysicsMaterial>("Spaceship");
            if (exteriorMaterial == null)
            {
                Debug.LogWarning($"[Ship] Could not load exterior physics material for {gameObject.name}");
            }
        }

        if (interiorMaterial == null)
        {
            interiorMaterial = Resources.Load<PhysicsMaterial>("ShipInterior");
            if (interiorMaterial == null)
            {
                Debug.LogWarning($"[Ship] Could not load interior physics material for {gameObject.name}");
            }
        }

        // Apply materials to colliders
        AssignPhysicsMaterials();
    }

    [ContextMenu("Assign Physics Materials")]
    void AssignPhysicsMaterials()
    {
        if (exteriorMaterial == null || interiorMaterial == null)
        {
            Debug.LogError($"[Ship] Physics materials not assigned on {gameObject.name}");
            return;
        }

        // Get all colliders in the ship hierarchy
        Collider[] colliders = GetComponentsInChildren<Collider>();

        int exteriorCount = 0;
        int interiorCount = 0;

        foreach (Collider col in colliders)
        {
            // Skip trigger colliders (like ShipGravity)
            if (col.isTrigger) continue;

            // Determine if this is an interior or exterior collider
            bool isInterior = IsInteriorCollider(col);

            if (isInterior)
            {
                col.material = interiorMaterial;
                interiorCount++;
            }
            else
            {
                col.material = exteriorMaterial;
                exteriorCount++;
            }
        }

        Debug.Log($"[Ship] Assigned materials to {exteriorCount} exterior and {interiorCount} interior colliders");
    }

    bool IsInteriorCollider(Collider collider)
    {
        string colliderName = collider.name.ToLower();

        // Check for interior keywords in collider name
        string[] interiorKeywords = { "interior", "floor", "wall", "ramp", "ceiling", "walkway", "cabin", "deck" };

        foreach (string keyword in interiorKeywords)
        {
            if (colliderName.Contains(keyword))
            {
                return true;
            }
        }

        // Check if the collider is inside the ship gravity trigger
        if (shipGravity != null)
        {
            Collider gravityTrigger = shipGravity.GetComponent<Collider>();
            if (gravityTrigger != null && gravityTrigger.bounds.Contains(collider.bounds.center))
            {
                return true;
            }
        }

        // Default to exterior if uncertain
        return false;
    }
}
