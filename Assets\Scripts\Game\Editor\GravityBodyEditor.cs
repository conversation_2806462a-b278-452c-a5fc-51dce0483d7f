using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(GravityAffector), true)]
[CanEditMultipleObjects]
public class GravityBodyEditor : Editor
{
	GravityAffector gravityAffector;
	bool showDebugInfo;

	public override void OnInspectorGUI()
	{
		DrawDefaultInspector();

		EditorGUILayout.Space(10);
		EditorGUILayout.LabelField("Debug", EditorStyles.boldLabel);
		showDebugInfo = EditorGUILayout.Foldout(showDebugInfo, "Debug info");
		if (showDebugInfo)
		{
			string[] gravityInfo = GetGravityInfo(gravityAffector.transform.position, gravityAffector.GetComponent<CelestialBody>());
			for (int i = 0; i < gravityInfo.Length; i++)
			{
				EditorGUILayout.LabelField(gravityInfo[i]);
			}

			// --- Show actual gravity source affecting the affector ---
			if (gravityAffector != null)
			{
				var src = gravityAffector.GetType().GetField("currentGravitySource", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?.GetValue(gravityAffector);
				EditorGUILayout.LabelField($"Actual gravity source: {src}");
				var dir = gravityAffector.GetGravityDirection();
				EditorGUILayout.LabelField($"Gravity direction: {dir}");
			}
		}
	}

	void OnEnable()
	{
		gravityAffector = target as GravityAffector;
		if (gravityAffector != null)
		{
			showDebugInfo = EditorPrefs.GetBool(gravityAffector.gameObject.name + nameof(showDebugInfo), false);
		}
	}

	void OnDisable()
	{
		if (gravityAffector != null)
		{
			EditorPrefs.SetBool(gravityAffector.gameObject.name + nameof(showDebugInfo), showDebugInfo);
		}
	}

	static string[] GetGravityInfo(Vector3 point, CelestialBody ignore = null)
	{
		CelestialBody[] bodies = FindObjectsByType<CelestialBody>(FindObjectsSortMode.None);
		Vector3 totalAcc = Vector3.zero;

		// gravity
		var forceAndName = new List<FloatAndString>();
		foreach (CelestialBody body in bodies)
		{
			if (body != ignore)
			{
				var offsetToBody = body.Position - point;
				var sqrDst = offsetToBody.sqrMagnitude;
				float dst = Mathf.Sqrt(sqrDst);
				var dirToBody = offsetToBody / Mathf.Sqrt(sqrDst);
				var acceleration = Universe.gravitationalConstant * body.mass / sqrDst;
				totalAcc += dirToBody * acceleration;
				forceAndName.Add(new FloatAndString() { floatVal = acceleration, stringVal = body.gameObject.name });
			}
		}
		forceAndName.Sort((a, b) => (b.floatVal.CompareTo(a.floatVal)));
		string[] info = new string[forceAndName.Count + 1];
		info[0] = $"acc: {totalAcc} (mag = {totalAcc.magnitude})";
		for (int i = 0; i < forceAndName.Count; i++)
		{
			info[i + 1] = $"acceleration due to {forceAndName[i].stringVal}: {forceAndName[i].floatVal}";
		}
		return info;
	}

	struct FloatAndString
	{
		public float floatVal;
		public string stringVal;
	}
}