// ───────────────────────────────────────
// GameSetUp.cs
// ───────────────────────────────────────
using UnityEngine;

/// <summary>
/// Places the player and the ship at scene start according to a simple preset:
///   • InShip  – player spawns already in the pilot seat, hatch closed.
///   • OnBody  – player and ship spawn just above a CelestialBody's surface,
///               sharing its orbital velocity so nothing "snaps" on frame 1.
/// </summary>
[DefaultExecutionOrder(-100)]             // run before most Awake/Start logic
public class GameSetUp : MonoBehaviour
{
	public enum StartCondition { InShip, OnBody }

	[Header("Start condition")]
	public StartCondition startCondition = StartCondition.InShip;

	[<PERSON>lt<PERSON>("Required for OnBody start")]
	public CelestialBody startBody;

	[<PERSON><PERSON>("OnBody parameters")]
	[Tooltip("How many radii above the surface the player & ship spawn")]
	[Range(1.01f, 5f)]
	public float heightFactor = 1.10f;

	[<PERSON><PERSON><PERSON>("Ship offset from player (local-right, in metres)")]
	public float shipLateralOffset = 20f;

	/* ────────────────────────────────────────────────────────── */
	void Start()
	{
		Ship ship = FindObjectOfType<Ship>(true);
		PlayerController player = FindObjectOfType<PlayerController>(true);

		if (ship == null || player == null)
		{
			Debug.LogError("[GameSetUp] Ship or PlayerController missing.");
			return;
		}

		switch (startCondition)
		{
			case StartCondition.InShip:
				SpawnInShip(ship, player);
				break;

			case StartCondition.OnBody:
				if (startBody != null) SpawnOnBody(ship, player);
				else Debug.LogError("[GameSetUp] Start Body not assigned.");
				break;
		}
	}

	/* ────────────────────────────────────────────────────────── */
	void SpawnInShip(Ship ship, PlayerController player)
	{
		// Already seated & camera re-parented
		if (!ship.IsPiloted) ship.TogglePiloting();

		// Make sure the player's GravityAffector is set to ship gravity
		ship.EnterShipInterior(player);    // harmless if already handled
	}

	void SpawnOnBody(Ship ship, PlayerController player)
	{
		// pick a 'right' vector from the body to place player & ship
		Vector3 surfaceNormal = Vector3.Normalize(player.transform.position -
												  startBody.transform.position);
		if (surfaceNormal == Vector3.zero) surfaceNormal = Vector3.right;

		Vector3 spawnPoint =
			startBody.transform.position +
			surfaceNormal * startBody.radius * heightFactor;

		// move & initialise player
		player.transform.position = spawnPoint;
		player.Rigidbody.linearVelocity = startBody.initialVelocity;
		player.ExitFromSpaceship();                      // ensure free-roam mode
		if (player.Camera.transform.parent != player.transform)
		{
			// If camera was still parented to ship (e.g. from editor testing),
			// restore default parenting.
			player.Camera.transform.SetParent(player.transform);
			player.Camera.transform.localPosition = player.CameraInitialLocalPos;
			player.Camera.transform.localRotation = Quaternion.identity;
		}

		// move ship nearby and match velocity
		ship.transform.position = spawnPoint + player.transform.right * shipLateralOffset;
		ship.GetComponent<Rigidbody>().linearVelocity = startBody.initialVelocity;

		// leave hatch open so the player can enter
		if (!ship.HatchOpen) ship.ToggleHatch();
	}
}
