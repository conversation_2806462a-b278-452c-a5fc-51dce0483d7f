using UnityEditor;
using UnityEngine;

/// <summary>
/// Test script to verify ship velocity inheritance is working correctly
/// </summary>
public class ShipVelocityTester : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private KeyCode testKey = KeyCode.V;
    [SerializeField] private bool enableContinuousMonitoring = false;
    [SerializeField] private bool showVelocityGizmos = true;

    [Header("Velocity Display")]
    [SerializeField] private bool showOnGUI = true;
    [SerializeField] private float gizmoScale = 1f;

    private PlayerController player;
    private Ship ship;
    private Rigidbody playerRb;
    private Rigidbody shipRb;

    void Start()
    {
        player = FindFirstObjectByType<PlayerController>();
        ship = FindFirstObjectByType<Ship>();

        if (player != null)
        {
            playerRb = player.GetComponent<Rigidbody>();
        }

        if (ship != null)
        {
            shipRb = ship.GetComponent<Rigidbody>();
        }
    }

    void Update()
    {
        if (Input.GetKeyDown(testKey))
        {
            TestVelocityInheritance();
        }

        if (enableContinuousMonitoring && Time.time % 1f < Time.deltaTime)
        {
            LogVelocityStatus();
        }
    }

    void TestVelocityInheritance()
    {
        if (player == null || ship == null)
        {
            Debug.LogError("[ShipVelocityTester] Player or Ship not found!");
            return;
        }

        Debug.Log("=== SHIP VELOCITY INHERITANCE TEST ===");

        // Check if player is in ship
        var currentShipField = player.GetType().GetField("currentShip",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var currentShip = currentShipField?.GetValue(player);

        bool playerInShip = currentShip != null;
        Debug.Log($"Player in ship: {playerInShip}");

        if (playerInShip)
        {
            // Test velocity inheritance
            Vector3 shipVelocity = shipRb != null ? shipRb.linearVelocity : Vector3.zero;
            Vector3 playerVelocity = playerRb != null ? playerRb.linearVelocity : Vector3.zero;

            Debug.Log($"Ship velocity: {shipVelocity}");
            Debug.Log($"Player velocity: {playerVelocity}");

            // Calculate velocity difference
            Vector3 velocityDiff = playerVelocity - shipVelocity;
            float velocityDiffMagnitude = velocityDiff.magnitude;

            Debug.Log($"Velocity difference: {velocityDiff} (magnitude: {velocityDiffMagnitude:F2})");

            // Check if velocities are synchronized
            if (velocityDiffMagnitude < 1f) // Allow small difference for player movement
            {
                Debug.Log("✅ GOOD: Player velocity is synchronized with ship!");
            }
            else if (velocityDiffMagnitude < 5f)
            {
                Debug.Log("⚠️ OKAY: Player velocity is mostly synchronized (small difference)");
            }
            else
            {
                Debug.Log("❌ PROBLEM: Player velocity is not synchronized with ship!");
            }

            // Test relative movement
            Vector3 playerRelativeVelocity = velocityDiff;
            Debug.Log($"Player relative velocity (movement within ship): {playerRelativeVelocity}");
        }
        else
        {
            Debug.Log("Player is not in ship - velocity inheritance not applicable");
        }

        Debug.Log("=== END VELOCITY TEST ===");
    }

    void LogVelocityStatus()
    {
        if (player == null || ship == null) return;

        var currentShipField = player.GetType().GetField("currentShip",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var currentShip = currentShipField?.GetValue(player);

        if (currentShip != null)
        {
            Vector3 shipVel = shipRb != null ? shipRb.linearVelocity : Vector3.zero;
            Vector3 playerVel = playerRb != null ? playerRb.linearVelocity : Vector3.zero;
            Vector3 diff = playerVel - shipVel;

            Debug.Log($"[VelocityMonitor] Ship: {shipVel.magnitude:F1} m/s, Player: {playerVel.magnitude:F1} m/s, Diff: {diff.magnitude:F1} m/s");
        }
    }

    [ContextMenu("Test Velocity Sync")]
    public void TestVelocitySync()
    {
        TestVelocityInheritance();
    }

    [ContextMenu("Force Player to Ship Velocity")]
    public void ForcePlayerToShipVelocity()
    {
        if (player == null || ship == null || playerRb == null || shipRb == null) return;

        playerRb.linearVelocity = shipRb.linearVelocity;
        Debug.Log($"[ShipVelocityTester] Forced player velocity to match ship: {shipRb.linearVelocity}");
    }

    void OnDrawGizmos()
    {
        if (!showVelocityGizmos) return;

        // Draw ship velocity
        if (ship != null && shipRb != null)
        {
            Gizmos.color = Color.blue;
            Vector3 shipPos = ship.transform.position;
            Vector3 shipVel = shipRb.linearVelocity * gizmoScale;
            Gizmos.DrawRay(shipPos, shipVel);
            Gizmos.DrawWireSphere(shipPos + shipVel, 0.5f);
        }

        // Draw player velocity
        if (player != null && playerRb != null)
        {
            Gizmos.color = Color.green;
            Vector3 playerPos = player.transform.position;
            Vector3 playerVel = playerRb.linearVelocity * gizmoScale;
            Gizmos.DrawRay(playerPos, playerVel);
            Gizmos.DrawWireSphere(playerPos + playerVel, 0.3f);
        }

        // Draw velocity difference if player is in ship
        if (player != null && ship != null && playerRb != null && shipRb != null)
        {
            var currentShipField = player.GetType().GetField("currentShip",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var currentShip = currentShipField?.GetValue(player);

            if (currentShip != null)
            {
                Gizmos.color = Color.red;
                Vector3 playerPos = player.transform.position;
                Vector3 velocityDiff = (playerRb.linearVelocity - shipRb.linearVelocity) * gizmoScale;
                Gizmos.DrawRay(playerPos, velocityDiff);
            }
        }
    }

    void OnGUI()
    {
        if (!showOnGUI) return;

        GUILayout.BeginArea(new Rect(10, 650, 400, 150));
        GUILayout.Label("Ship Velocity Tester", EditorStyles.boldLabel);
        GUILayout.Label($"Press {testKey} to test velocity inheritance");

        if (player != null && ship != null)
        {
            var currentShipField = player.GetType().GetField("currentShip",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var currentShip = currentShipField?.GetValue(player);

            if (currentShip != null)
            {
                Vector3 shipVel = shipRb != null ? shipRb.linearVelocity : Vector3.zero;
                Vector3 playerVel = playerRb != null ? playerRb.linearVelocity : Vector3.zero;
                Vector3 diff = playerVel - shipVel;

                GUILayout.Label($"Ship Velocity: {shipVel.magnitude:F1} m/s");
                GUILayout.Label($"Player Velocity: {playerVel.magnitude:F1} m/s");
                GUILayout.Label($"Difference: {diff.magnitude:F1} m/s");

                if (diff.magnitude < 1f)
                {
                    GUI.color = Color.green;
                    GUILayout.Label("✅ Velocities Synchronized");
                }
                else if (diff.magnitude < 5f)
                {
                    GUI.color = Color.yellow;
                    GUILayout.Label("⚠️ Minor Desync");
                }
                else
                {
                    GUI.color = Color.red;
                    GUILayout.Label("❌ Major Desync");
                }
                GUI.color = Color.white;
            }
            else
            {
                GUILayout.Label("Player not in ship");
            }
        }

        GUILayout.EndArea();
    }
}
