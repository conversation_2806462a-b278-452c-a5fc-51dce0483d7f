﻿// ───────────────────────────────────────
// PlayerController.cs  • clean version
// ───────────────────────────────────────
using UnityEngine;

[RequireComponent(typeof(Rigidbody))]
public class PlayerController : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
{
    /* ── Movement ─────────────────────────────────────────────── */
    [Header("Movement settings")]
    public float walkSpeed = 8f;
    public float runSpeed = 14f;
    public float jumpForce = 20f;
    public float vSmoothTime = 0.10f;
    public float airSmoothTime = 0.50f;
    public float stickToGroundForce = 8f;
    public float groundCheckDistance = 0.10f;
    public float slopeLimit = 45f;
    public float slideSpeed = 5f;
    public float airControl = 0.10f;

    /* ── Jet-pack ─────────────────────────────────────────────── */
    [Head<PERSON>("Jet-pack settings")]
    public float jetpackForce = 10f;
    public float jetpackDuration = 2f;
    public float jetpackRefuelTime = 2f;
    public float jetpackRefuelDelay = 2f;

    /* ── Mouse ────────────────────────────────────────────────── */
    [Header("Mouse settings")]
    public float mouseSensitivityMultiplier = 1f;
    public float maxMouseSmoothTime = 0.3f;
    public Vector2 pitchMinMax = new Vector2(-40, 85);
    public InputSettings inputSettings;

    /* ── References ───────────────────────────────────────────── */
    [Header("References")]
    public Transform cameraTransform;             // main camera
    public Transform groundCheck;                 // sphere cast origin
    public Transform modelTransform;              // purely visual (optional)
    public LayerMask groundMask;

    /* ── Private state ────────────────────────────────────────── */
    Rigidbody rb;
    GravityAffector gravityAffector;
    Ship currentShip;

    bool isGrounded;
    bool usingJetpack;
    float jetpackFuelPercent = 1f;
    float lastJetpackUseTime;

    bool cursorLocked = true;

    // camera rotation
    float yaw, pitch, smoothYaw, smoothPitch;
    float yawSmoothV, pitchSmoothV;
    Vector3 smoothVelRef;

    public Vector3 CameraInitialLocalPos { get; private set; }

    /* ─────────────────────────────────────────────────────────── */
    #region Unity-lifecycle
    void Awake()
    {
        rb = GetComponent<Rigidbody>();
        rb.useGravity = false;
        rb.interpolation = RigidbodyInterpolation.Interpolate;
        rb.mass = 70f;

        gravityAffector = GetComponent<GravityAffector>() ??
                          gameObject.AddComponent<GravityAffector>();

        // fallback assignments
        cameraTransform ??= GetComponentInChildren<Camera>()?.transform;
        groundCheck ??= transform.Find("Feet");
        modelTransform ??= transform.Find("Model");

        if (cameraTransform == null)
        {
            Debug.LogError("PlayerController: no camera found.");
            enabled = false;
            return;
        }

        CameraInitialLocalPos = cameraTransform.localPosition;

        // Ensure input settings are properly initialized
        if (inputSettings == null)
        {
            inputSettings = ScriptableObject.CreateInstance<InputSettings>();
            inputSettings.mouseSensitivity = 100f;  // Default sensitivity
            inputSettings.mouseSmoothing = 0.2f;    // Default smoothing
        }
        inputSettings.Begin();

        yaw = transform.eulerAngles.y;
        pitch = cameraTransform.localEulerAngles.x;
        smoothYaw = yaw;
        smoothPitch = pitch;

        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }

    void Update()
    {
        ReadInput();
        RotateCamera();
        UpdateJetpackFuel();
    }

    void FixedUpdate()
    {
        Vector3 gDir = currentShip
                     ? currentShip.GetArtificialGravityDirection()
                     : gravityAffector.GetGravityDirection();

        AlignToGravity(gDir);
        CheckGround(gDir);
        MoveCharacter(gDir);
    }
    #endregion

    /* ── Input / camera ───────────────────────────────────────── */

    void ReadInput()
    {
        if (Input.GetKeyDown(KeyCode.Space) && isGrounded)
            Jump();

        usingJetpack = Input.GetKey(KeyCode.Space) &&
                       !isGrounded &&
                       jetpackFuelPercent > 0f;

        if (Input.GetKeyDown(KeyCode.Escape))
        {
            cursorLocked = !cursorLocked;
            Cursor.lockState = cursorLocked ? CursorLockMode.Locked
                                            : CursorLockMode.None;
            Cursor.visible = !cursorLocked;
        }
    }

    void RotateCamera()
    {
        if (!cursorLocked) return;

        float mx = Input.GetAxisRaw("Mouse X");
        float my = Input.GetAxisRaw("Mouse Y");

        // Debug mouse input if needed
        if (Input.GetKeyDown(KeyCode.M))
        {
            Debug.Log($"Mouse Input - X: {mx}, Y: {my}, Sensitivity: {inputSettings.mouseSensitivity}, Locked: {cursorLocked}");
        }

        float sens = inputSettings.mouseSensitivity * mouseSensitivityMultiplier;

        yaw += mx * sens * Time.deltaTime;
        pitch -= my * sens * Time.deltaTime;
        pitch = Mathf.Clamp(pitch, pitchMinMax.x, pitchMinMax.y);

        float smTime = Mathf.Lerp(0.01f, maxMouseSmoothTime,
                                    inputSettings.mouseSmoothing);
        smoothPitch = Mathf.SmoothDampAngle(smoothPitch, pitch,
                                               ref pitchSmoothV, smTime);
        float oldYaw = smoothYaw;
        smoothYaw = Mathf.SmoothDampAngle(smoothYaw, yaw,
                                               ref yawSmoothV, smTime);

        cameraTransform.localEulerAngles = Vector3.right * smoothPitch;
        transform.Rotate(Vector3.up * Mathf.DeltaAngle(oldYaw, smoothYaw),
                         Space.Self);
    }

    /* ── Character movement ───────────────────────────────────── */

    void MoveCharacter(Vector3 gDir)
    {
        float h = Input.GetAxisRaw("Horizontal");
        float v = Input.GetAxisRaw("Vertical");
        bool run = Input.GetKey(KeyCode.LeftShift);

        Vector3 input = new(h, 0f, v);
        if (input.sqrMagnitude > 1f) input.Normalize();

        float speed = run ? runSpeed : walkSpeed;
        Vector3 targetVel = transform.TransformDirection(input) * speed;
        targetVel = Vector3.ProjectOnPlane(targetVel, gDir);

        Vector3 lateral = Vector3.ProjectOnPlane(rb.linearVelocity, gDir);
        Vector3 vertical = Vector3.Project(rb.linearVelocity, gDir);

        float smooth = isGrounded ? vSmoothTime : airSmoothTime;
        lateral = Vector3.SmoothDamp(lateral, targetVel,
                                                ref smoothVelRef, smooth);

        rb.linearVelocity = lateral + vertical;

        if (usingJetpack)
            rb.AddForce(-gDir * jetpackForce, ForceMode.Acceleration);

        if (isGrounded)
            rb.AddForce(-gDir * stickToGroundForce, ForceMode.Acceleration);
    }

    void CheckGround(Vector3 gDir)
    {
        if (groundCheck == null) { isGrounded = false; return; }

        // Use a slightly larger check distance for better detection
        float checkDist = groundCheckDistance * 1.2f;
        isGrounded = Physics.CheckSphere(groundCheck.position, checkDist, groundMask);

        // Additional raycast check for more reliable ground detection
        if (!isGrounded)
        {
            isGrounded = Physics.Raycast(groundCheck.position, gDir, checkDist * 2f, groundMask);
        }

        if (!isGrounded) return;

        // slope handling
        if (Physics.Raycast(groundCheck.position, gDir,
                            out var hit, checkDist * 2f, groundMask))
        {
            float angle = Vector3.Angle(hit.normal, -gDir);
            if (angle > slopeLimit)
            {
                Vector3 slideDir =
                    Vector3.ProjectOnPlane(gDir, hit.normal).normalized;
                rb.linearVelocity += slideDir * slideSpeed * Time.deltaTime;
            }
        }
    }

    void Jump()
    {
        Vector3 gDir = currentShip
                     ? currentShip.GetArtificialGravityDirection()
                     : gravityAffector.GetGravityDirection();

        rb.AddForce(-gDir * jumpForce, ForceMode.Impulse);
    }

    /* ── Jet-pack fuel ───────────────────────────────────────── */

    void UpdateJetpackFuel()
    {
        if (usingJetpack)
        {
            jetpackFuelPercent =
                Mathf.Max(0f, jetpackFuelPercent -
                               (1f / jetpackDuration) * Time.deltaTime);
            lastJetpackUseTime = Time.time;
        }
        else if (Time.time - lastJetpackUseTime > jetpackRefuelDelay)
        {
            jetpackFuelPercent =
                Mathf.Min(1f, jetpackFuelPercent +
                               (1f / jetpackRefuelTime) * Time.deltaTime);
        }
    }

    /* ── Gravity alignment ───────────────────────────────────── */
    void AlignToGravity(Vector3 gDir)
    {
        Vector3 up = -gDir;
        // retain yaw by projecting current forward onto plane
        Vector3 fwd = Vector3.ProjectOnPlane(transform.forward, up);
        if (fwd.sqrMagnitude < 0.001f)
            fwd = Vector3.ProjectOnPlane(cameraTransform.forward, up);

        Quaternion target = Quaternion.LookRotation(fwd, up);
        transform.rotation =
            Quaternion.Slerp(transform.rotation, target, Time.deltaTime * 15f);

        // optional: keep suit model perfectly upright relative to body
        if (modelTransform != null)
            modelTransform.localRotation = Quaternion.identity;
    }

    /* ── Ship hooks ──────────────────────────────────────────── */
    public void EnterShipInterior(Ship ship)
    {
        currentShip = ship;
        gravityAffector.SwitchToInteriorGravity(
            ship.GetComponent<ShipGravity>());

        // inherit current ship velocity to prevent sudden snap
        rb.linearVelocity =
            ship.GetComponent<Rigidbody>().linearVelocity;
    }

    public void ExitFromSpaceship()
    {
        gravityAffector.SwitchToPlanetaryGravity();
        currentShip = null;
    }

    /* ── Public conveniences ─────────────────────────────────── */
    public Camera Camera => cameraTransform.GetComponent<Camera>();
    public Rigidbody Rigidbody => rb;
}
