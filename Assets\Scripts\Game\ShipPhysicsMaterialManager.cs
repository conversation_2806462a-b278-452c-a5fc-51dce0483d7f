using UnityEngine;

/// <summary>
/// Manages physics materials for ship components to ensure proper friction behavior
/// for both exterior (landing) and interior (player movement) surfaces.
/// </summary>
public class ShipPhysicsMaterialManager : MonoBehaviour
{
    [Header("Physics Materials")]
    [SerializeField] private PhysicsMaterial exteriorMaterial;  // High friction for landing
    [SerializeField] private PhysicsMaterial interiorMaterial;  // Low friction for player movement
    
    [Header("Configuration")]
    [SerializeField] private bool autoAssignOnStart = true;
    [SerializeField] private string[] interiorColliderNames = { 
        "Interior", "Floor", "Wall", "Ramp", "Ceiling", "Walkway" 
    };
    
    void Start()
    {
        if (autoAssignOnStart)
        {
            AssignPhysicsMaterials();
        }
    }
    
    /// <summary>
    /// Automatically assigns physics materials to ship colliders based on naming conventions
    /// </summary>
    [ContextMenu("Assign Physics Materials")]
    public void AssignPhysicsMaterials()
    {
        if (exteriorMaterial == null || interiorMaterial == null)
        {
            Debug.LogError($"[ShipPhysicsMaterialManager] Physics materials not assigned on {gameObject.name}");
            return;
        }
        
        // Get all colliders in the ship hierarchy
        Collider[] colliders = GetComponentsInChildren<Collider>();
        
        int exteriorCount = 0;
        int interiorCount = 0;
        
        foreach (Collider col in colliders)
        {
            // Skip trigger colliders (like ShipGravity)
            if (col.isTrigger) continue;
            
            // Determine if this is an interior or exterior collider
            bool isInterior = IsInteriorCollider(col);
            
            if (isInterior)
            {
                col.material = interiorMaterial;
                interiorCount++;
                Debug.Log($"[ShipPhysicsMaterialManager] Assigned interior material to: {col.name}");
            }
            else
            {
                col.material = exteriorMaterial;
                exteriorCount++;
                Debug.Log($"[ShipPhysicsMaterialManager] Assigned exterior material to: {col.name}");
            }
        }
        
        Debug.Log($"[ShipPhysicsMaterialManager] Assigned materials to {exteriorCount} exterior and {interiorCount} interior colliders");
    }
    
    /// <summary>
    /// Determines if a collider should be considered an interior surface
    /// </summary>
    private bool IsInteriorCollider(Collider collider)
    {
        string colliderName = collider.name.ToLower();
        
        // Check if the collider name contains any interior keywords
        foreach (string keyword in interiorColliderNames)
        {
            if (colliderName.Contains(keyword.ToLower()))
            {
                return true;
            }
        }
        
        // Check if the collider is inside the ship gravity trigger
        ShipGravity shipGravity = GetComponentInChildren<ShipGravity>();
        if (shipGravity != null)
        {
            Collider gravityTrigger = shipGravity.GetComponent<Collider>();
            if (gravityTrigger != null && gravityTrigger.bounds.Contains(collider.bounds.center))
            {
                return true;
            }
        }
        
        // Default to exterior if uncertain
        return false;
    }
    
    /// <summary>
    /// Manually assign a collider as interior surface
    /// </summary>
    public void SetAsInteriorSurface(Collider collider)
    {
        if (interiorMaterial != null && collider != null && !collider.isTrigger)
        {
            collider.material = interiorMaterial;
            Debug.Log($"[ShipPhysicsMaterialManager] Manually set {collider.name} as interior surface");
        }
    }
    
    /// <summary>
    /// Manually assign a collider as exterior surface
    /// </summary>
    public void SetAsExteriorSurface(Collider collider)
    {
        if (exteriorMaterial != null && collider != null && !collider.isTrigger)
        {
            collider.material = exteriorMaterial;
            Debug.Log($"[ShipPhysicsMaterialManager] Manually set {collider.name} as exterior surface");
        }
    }
}
